<?php

namespace Config;

use CodeIgniter\Config\BaseConfig;

class Upload extends BaseConfig
{
    /**
     * Default upload configuration for community images
     */
    public array $community = [
        'upload_path' => 'writable/uploads/community/',
        'allowed_types' => ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
        'max_size' => 5242880, // 5MB in bytes
        'max_width' => 2048,
        'max_height' => 2048,
        'encrypt_name' => true,
        'remove_spaces' => true,
    ];

    /**
     * Get upload paths in order of preference
     *
     * @return array
     */
    public function getUploadPaths(): array
    {
        return [
            'writable' => WRITEPATH . 'uploads/community/',
            'public' => FCPATH . 'uploads/community/',
            'public_alt' => ROOTPATH . 'public/uploads/community/',
        ];
    }

    /**
     * Get web URL for uploaded file
     *
     * @param string $filename
     * @return string
     */
    public function getWebUrl(string $filename): string
    {
        // Since files are stored in writable directory, serve them through controller
        return base_url('community/image/' . $filename);
    }

    /**
     * Get relative path for database storage
     *
     * @param string $filename
     * @return string
     */
    public function getRelativePath(string $filename): string
    {
        return $this->community['upload_path'] . $filename;
    }

    /**
     * Validate file type
     * 
     * @param string $mimeType
     * @return bool
     */
    public function isValidType(string $mimeType): bool
    {
        return in_array($mimeType, $this->community['allowed_types']);
    }

    /**
     * Validate file size
     * 
     * @param int $size
     * @return bool
     */
    public function isValidSize(int $size): bool
    {
        return $size <= $this->community['max_size'];
    }

    /**
     * Get human readable max size
     * 
     * @return string
     */
    public function getMaxSizeFormatted(): string
    {
        $bytes = $this->community['max_size'];
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get allowed file extensions for display
     *
     * @return string
     */
    public function getAllowedTypesFormatted(): string
    {
        $extensions = [];
        foreach ($this->community['allowed_types'] as $type) {
            switch ($type) {
                case 'image/jpeg':
                case 'image/jpg':
                    $extensions[] = 'JPG';
                    break;
                case 'image/png':
                    $extensions[] = 'PNG';
                    break;
                case 'image/gif':
                    $extensions[] = 'GIF';
                    break;
                case 'image/webp':
                    $extensions[] = 'WebP';
                    break;
            }
        }

        return implode(', ', array_unique($extensions));
    }

    /**
     * Convert full URL to relative path for database storage
     *
     * @param string $url
     * @return string
     */
    public function convertToRelativePath(string $url): string
    {
        // If it's already a relative path, return as is
        if (!str_contains($url, 'http')) {
            return $url;
        }

        // Extract filename from URL
        $filename = basename($url);

        // Validate it's an image file
        if ($filename && preg_match('/\.(jpg|jpeg|png|gif|webp)$/i', $filename)) {
            return $this->community['upload_path'] . $filename;
        }

        // If we can't parse it properly, return the original
        return $url;
    }

    /**
     * Convert relative path to full URL for display
     *
     * @param string $path
     * @return string
     */
    public function convertToFullUrl(string $path): string
    {
        // If it's already a full URL, return as is
        if (str_starts_with($path, 'http')) {
            return $path;
        }

        // Extract filename from path
        $filename = basename($path);

        // Return URL that serves through controller
        return base_url('community/image/' . $filename);
    }
}
